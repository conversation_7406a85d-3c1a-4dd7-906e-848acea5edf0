<div *ngIf="canViewAllUsers || canViewReportees">
  <div class="d-flex bg-white py-12 px-16 border-top border-bottom">
    <div class="align-center ml-8 z-index-1021 tb-px-0 tb-pb-0 tb-br-top-unset tb-left-110 left-230"
      [ngClass]="showLeftNav ? 'left-230' : 'left-125'">
      <ul class="align-center top-nav-bar text-nowrap">
        <ng-container *ngFor="let visibilityImage of visibilityList;let i=index">
          <div [title]="usersData?.[visibilityImage?.visibility] ? usersData?.[visibilityImage?.visibility] : ''"
            (click)="currentVisibility(visibilityImage.userStatus, true)" class="cursor-pointer">
            <div class="align-center ph-mb-4">
              <a [class.active]="appliedFilter.userStatus == visibilityImage.userStatus"><img
                  [type]="'leadrat'" [appImage]="s3BucketUrl + visibilityImage.image" alt="muso" width="22" height="22"></a>
              <span [class.active]="appliedFilter.userStatus == visibilityImage.userStatus"
                class="text-large ml-8 mr-16">{{ visibilityImage.name }}</span>
            </div>
          </div>
        </ng-container>
      </ul>
    </div>
  </div>
  <!-- <div class="mt-16 fw-600 header-3">{{ 'GLOBAL.lead' | translate }} User vs Sub-Source {{
    'SIDEBAR.reports' | translate }}</div> -->
  <div class="mx-24">
    <ng-template #AdvancedFilters>
      <!-- Filter Container matching Figma design -->
      <div style="background: #FFFFFF; border-radius: 0px 0px 16px 16px; width: 925px; height: 300px; position: relative;">
        <!-- Top Section -->
        <div style="padding: 28px; display: flex; flex-direction: column; align-items: flex-end; gap: 16px;">
          <!-- Filter Title and Date Section -->
          <div style="display: flex; justify-content: space-between; align-items: center; width: 869px; gap: 92px;">
            <h4 style="font-family: 'Lexend Deca', sans-serif; font-weight: 600; font-size: 14px; line-height: 1.14; text-transform: uppercase; color: #626262; margin: 0;">
              FILTER
            </h4>

            <!-- Date Filter Section -->
            <div style="display: flex; align-items: center; width: 267px;">
              <!-- Date Type Dropdown -->
              <div style="border: 1px solid #EEEEEE; border-radius: 6px 0px 0px 6px; padding: 11px 7px; width: 92px; display: flex; flex-direction: column; gap: 10px;">
                <div style="display: flex; align-items: center;">
                  <ng-select [virtualScroll]="true" placeholder="All" [searchable]="false"
                    style="border: none; background: transparent; font-family: 'Lexend Deca', sans-serif; font-weight: 500; font-size: 12px; color: #000000; width: 70px;"
                    [(ngModel)]="appliedFilter.dateType">
                    <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                      [value]="dType">{{dType}}</ng-option>
                  </ng-select>
                  <svg width="10" height="4" viewBox="0 0 10 4" fill="none" style="margin-left: auto;">
                    <path d="M1 1L5 3L9 1" stroke="#595667" stroke-width="1"/>
                  </svg>
                </div>
              </div>

              <!-- Date Range -->
              <div style="position: relative; width: 175px; height: 32px;">
                <div style="border: 1px solid #EEEEEE; border-radius: 6px 0px 0px 6px; width: 175px; height: 32px; position: absolute;"></div>
                <div style="position: absolute; top: 7px; left: 7.97px; display: flex; align-items: center; gap: 3px;">
                  <div style="width: 18px; height: 18px; position: relative;">
                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" style="position: absolute; top: 2px; left: 2px;" [owlDateTimeTrigger]="dt1">
                      <path d="M11.6667 2.33333H2.33333C1.59695 2.33333 1 2.93029 1 3.66667V11.6667C1 12.403 1.59695 13 2.33333 13H11.6667C12.403 13 13 12.403 13 11.6667V3.66667C13 2.93029 12.403 2.33333 11.6667 2.33333Z" stroke="#595667" stroke-width="1" fill="none"/>
                      <path d="M9.66667 1V3.66667" stroke="#595667" stroke-width="1"/>
                      <path d="M4.33333 1V3.66667" stroke="#595667" stroke-width="1"/>
                      <path d="M1 6.33333H13" stroke="#595667" stroke-width="1"/>
                    </svg>
                  </div>
                  <input type="text" readonly placeholder="09-08-2022 - 21-09-2023"
                    style="border: none; background: transparent; font-family: 'Lexend Deca', sans-serif; font-weight: 400; font-size: 12px; color: #000000; width: 140px;"
                    [max]="appliedFilter.dateType === 'Modified Date' || appliedFilter.dateType === 'Created Date' ? maxDate : ''"
                    [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                    (ngModelChange)="appliedFilter.date = $event" [ngModel]="appliedFilter.date"
                    [disabled]="!appliedFilter.dateType" />
                  <owl-date-time [pickerType]="'calendar'" #dt1 (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                </div>
              </div>
            </div>
          </div>

          <!-- Filter Fields Grid -->
          <div style="display: flex; flex-wrap: wrap; gap: 24px; width: 869px; height: 144px; overflow-y: auto;">
            <!-- Users Filter -->
            <div style="display: flex; flex-direction: column; gap: 4px; width: 270px;">
              <div style="display: flex; justify-content: space-between; align-items: end; gap: 4px;">
                <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 600; font-size: 12px; text-transform: uppercase; color: #343739;">Users</span>
                <label class="checkbox-container mb-4" style="font-size: 10px; color: #343739;">
                  <input type="checkbox" [(ngModel)]="appliedFilter.withTeam">
                  <span class="checkmark"></span>With Team
                </label>
              </div>
              <div style="background: #FBFBFB; border: 1px solid #DDDDDD; border-radius: 5px; width: 270px; height: 34px; position: relative;">
                <ng-select [virtualScroll]="true" [items]="canViewAllUsers ? allUsers : onlyReportees"
                  [ngClass]="{'blinking pe-none': (canViewAllUsers ? isAllUsersLoading : isOnlyReporteesLoading )}"
                  [multiple]="true" [closeOnSelect]="false" name="user" placeholder="Select"
                  [(ngModel)]="appliedFilter.users" bindLabel="fullName" bindValue="id"
                  style="border: none; background: transparent; width: 100%; height: 100%; padding: 14px;">
                  <ng-template ng-label-tmp let-item="item" let-clear="clear">
                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                    <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="flex-between">
                      <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                          data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                          class="checkmark"></span><span class="text-truncate-1 break-all"> {{item.firstName}}
                          {{item.lastName}}</span>
                      </div>
                      <span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                    </div>
                  </ng-template>
                </ng-select>
                <div style="position: absolute; top: 50%; right: 14px; transform: translateY(-50%); width: 24px; height: 24px; pointer-events: none;">
                  <svg width="12" height="5.33" viewBox="0 0 12 5.33" fill="none" style="position: absolute; top: 9.33px; left: 6px;">
                    <path d="M1 1L6 4.33L11 1" stroke="#343739" stroke-width="1"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Country Filter -->
            <div style="display: flex; flex-direction: column; gap: 4px; width: 270px;">
              <div style="display: flex; justify-content: flex-start; gap: 4px;">
                <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 600; font-size: 12px; text-transform: uppercase; color: #343739;">Country</span>
              </div>
              <div style="background: #FBFBFB; border: 1px solid #DDDDDD; border-radius: 5px; width: 270px; height: 34px; position: relative;">
                <ng-select [virtualScroll]="true" [items]="countryList"
                  [ngClass]="{'blinking pe-none': countryIsLoading}" [multiple]="true"
                  [closeOnSelect]="false" placeholder="Select" bindLabel="item"
                  bindValue="item" [(ngModel)]="appliedFilter.Countries" (change)="onCountryChange()"
                  style="border: none; background: transparent; width: 100%; height: 100%; padding: 14px;">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all"> {{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
                <div style="position: absolute; top: 50%; right: 14px; transform: translateY(-50%); width: 24px; height: 24px; pointer-events: none;">
                  <svg width="12" height="5.33" viewBox="0 0 12 5.33" fill="none" style="position: absolute; top: 9.33px; left: 6px;">
                    <path d="M1 1L6 4.33L11 1" stroke="#343739" stroke-width="1"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- City Filter -->
            <div style="display: flex; flex-direction: column; gap: 4px; width: 270px;">
              <div style="display: flex; justify-content: flex-start; gap: 4px;">
                <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 600; font-size: 12px; text-transform: uppercase; color: #343739;">City</span>
              </div>
              <div style="background: #FBFBFB; border: 1px solid #DDDDDD; border-radius: 5px; width: 270px; height: 34px; position: relative;">
                <ng-select [virtualScroll]="true" [items]="cities" [ngClass]="{'blinking pe-none': citiesIsLoading}"
                  [multiple]="true" [closeOnSelect]="false"
                  placeholder="Select" bindLabel="item" bindValue="item"
                  [(ngModel)]="appliedFilter.cities"
                  style="border: none; background: transparent; width: 100%; height: 100%; padding: 14px;">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all"> {{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
                <div style="position: absolute; top: 50%; right: 14px; transform: translateY(-50%); width: 24px; height: 24px; pointer-events: none;">
                  <svg width="12" height="5.33" viewBox="0 0 12 5.33" fill="none" style="position: absolute; top: 9.33px; left: 6px;">
                    <path d="M1 1L6 4.33L11 1" stroke="#343739" stroke-width="1"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- State Filter -->
            <div style="display: flex; flex-direction: column; gap: 4px; width: 270px;">
              <div style="display: flex; justify-content: flex-start; gap: 4px;">
                <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 600; font-size: 12px; text-transform: uppercase; color: #343739;">State</span>
              </div>
              <div style="background: #FBFBFB; border: 1px solid #DDDDDD; border-radius: 5px; width: 270px; height: 34px; position: relative;">
                <ng-select [virtualScroll]="true" [items]="states" [ngClass]="{'blinking pe-none': statesIsLoading}"
                  [multiple]="true" [closeOnSelect]="false"
                  placeholder="Select" bindLabel="item" bindValue="item"
                  [(ngModel)]="appliedFilter.states" (change)="onStateChange()"
                  style="border: none; background: transparent; width: 100%; height: 100%; padding: 14px;">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all"> {{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
                <div style="position: absolute; top: 50%; right: 14px; transform: translateY(-50%); width: 24px; height: 24px; pointer-events: none;">
                  <svg width="12" height="5.33" viewBox="0 0 12 5.33" fill="none" style="position: absolute; top: 9.33px; left: 6px;">
                    <path d="M1 1L6 4.33L11 1" stroke="#343739" stroke-width="1"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Payment Mode Filter -->
            <div style="display: flex; flex-direction: column; gap: 4px; width: 270px; height: 59px;">
              <div style="display: flex; justify-content: flex-start; gap: 4px;">
                <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 600; font-size: 12px; text-transform: uppercase; color: #343739;">Payment Mode</span>
              </div>
              <div style="background: #FBFBFB; border: 1px solid #DDDDDD; border-radius: 5px; width: 270px; height: 34px; position: relative;">
                <ng-select [virtualScroll]="true" [items]="paymentModes" [multiple]="true"
                  [closeOnSelect]="false" placeholder="Select" bindLabel="item"
                  bindValue="item" [(ngModel)]="appliedFilter.paymentModes"
                  style="border: none; background: transparent; width: 100%; height: 100%; padding: 14px;">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all"> {{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
                <div style="position: absolute; top: 50%; right: 14px; transform: translateY(-50%); width: 24px; height: 24px; pointer-events: none;">
                  <svg width="12" height="5.33" viewBox="0 0 12 5.33" fill="none" style="position: absolute; top: 9.33px; left: 6px;">
                    <path d="M1 1L6 4.33L11 1" stroke="#343739" stroke-width="1"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Project Filter -->
            <div style="display: flex; flex-direction: column; gap: 4px; width: 270px; height: 59px;">
              <div style="display: flex; justify-content: flex-start; gap: 4px;">
                <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 600; font-size: 12px; text-transform: uppercase; color: #343739; width: 56px;">Project</span>
              </div>
              <div style="background: #FBFBFB; border: 1px solid #DDDDDD; border-radius: 5px; width: 270px; height: 34px; position: relative;">
                <ng-select [virtualScroll]="true" [items]="projectList"
                  [ngClass]="{'blinking pe-none': isProjectListLoading}" [multiple]="true" [closeOnSelect]="false"
                  placeholder="Select" bindLabel="id"
                  [(ngModel)]="appliedFilter.projects"
                  style="border: none; background: transparent; width: 100%; height: 100%; padding: 14px;">
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                        class="checkmark"></span><span class="text-truncate-1 break-all"> {{item}}</span>
                    </div>
                  </ng-template>
                </ng-select>
                <div style="position: absolute; top: 50%; right: 14px; transform: translateY(-50%); width: 24px; height: 24px; pointer-events: none;">
                  <svg width="12" height="5.33" viewBox="0 0 12 5.33" fill="none" style="position: absolute; top: 9.33px; left: 6px;">
                    <path d="M1 1L6 4.33L11 1" stroke="#343739" stroke-width="1"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bottom Section with Buttons -->
        <div style="background: #FFFFFF; border-top: 1px solid #EEEEEE; box-shadow: 0px -2px 16px 0px rgba(0, 0, 0, 0.1); display: flex; justify-content: flex-end; align-items: center; gap: 14px; padding: 10px 20px; position: absolute; bottom: 0; left: 0; width: 925px; box-sizing: border-box;">
          <div style="display: flex; justify-content: flex-end; align-items: center; gap: 6px;">
            <!-- Cancel Button -->
            <div style="display: flex; justify-content: center; align-items: center; gap: 10px; padding: 10px 6px; border-radius: 4px; cursor: pointer;" (click)="modalService.hide()">
              <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 500; font-size: 12px; color: #494F56;">Cancel</span>
            </div>

            <!-- Separator -->
            <div style="width: 0; height: 14px; border-left: 1px solid #DDDDDD;"></div>

            <!-- Reset Button -->
            <div style="display: flex; justify-content: center; align-items: center; gap: 10px; padding: 10px 6px; border-radius: 4px; cursor: pointer;" (click)="reset()">
              <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 500; font-size: 12px; color: #494F56;">Reset</span>
            </div>

            <!-- Separator -->
            <div style="width: 0; height: 14px; border-left: 1px solid #DDDDDD;"></div>

            <!-- Search Button -->
            <div style="background: #121212; display: flex; justify-content: center; align-items: center; gap: 10px; padding: 10px 22px; border-radius: 4px; cursor: pointer;" (click)="applyAdvancedFilter()">
              <span style="font-family: 'Lexend Deca', sans-serif; font-weight: 500; font-size: 12px; color: #FFFFFF;">Search</span>
            </div>
          </div>
        </div>
      </div>
    </ng-template>
    <div class="pt-20">
      <div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
        <div class="align-center border-end flex-grow-1 no-validation">
          <ng-container>
            <div class="align-center w-100 px-10 py-12">
              <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
              <input placeholder="Search by User" (keydown)="onSearch($event)" (input)="isEmptyInput($event)"
                autocomplete="off" name="search" [(ngModel)]="searchTerm" class="border-0 outline-0 w-100">
            </div>
            <small class="text-muted text-nowrap ph-d-none pr-6">({{ 'LEADS.lead-search-prompt' | translate }})</small>
          </ng-container>
          <div *ngIf="canExportAllUsers || canExportReportees"
            class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-end"
            (click)="exportSourceReport()">{{ 'REPORTS.export' | translate }}</div>
        </div>
        <div class="d-flex tb-br-top">
          <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1"
            (click)="openAdvFiltersModal(AdvancedFilters)">
            <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
            <span class="fw-600">{{'PROPERTY.advanced-filters' | translate}}</span>
          </div>
          <div class="show-dropdown-white align-center position-relative">
            <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">{{ 'GLOBAL.show' |
                translate
                }}</span> {{ 'GLOBAL.entries' |
              translate }}</span>
            <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-150 tb-w-120px"
              ResizableDropdown [(ngModel)]="selectedPageSize" (change)="assignCount()" [searchable]="false">
              <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                {{pageSize}}</ng-option>
            </ng-select>
          </div>
        </div>
      </div>
      <div class="bg-white px-4 py-12 tb-w-100-34" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
        <ng-container *ngIf="showFilters">
          <div class="bg-secondary flex-between">
            <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
              <div class="d-flex" *ngFor="let filter of appliedFilter | keyvalue">
                <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                  *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                  {{reportFiltersKeyLabel[filter.key] || filter.key}}:
                  {{ filter.key === 'users' ? getUserName(value) :
                  value}}
                  <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                    (click)="onRemoveFilter(filter.key, value)"></span>
                </div>
              </div>
            </drag-scroll>
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
              (click)="reset()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
            </div>
          </div>
        </ng-container>
      </div>
      <ng-template #statusData>
        <ng-container *ngIf="!isRevSourceReportLoading">
          <div class="reports pinned-grid ag-grid-grouping">
            <ag-grid-angular #agGrid class="ag-theme-alpine" [pagination]="true" [paginationPageSize]="pageSize + 1"
              [gridOptions]="gridOptions" [rowData]="rowData" [suppressPaginationPanel]="true"
              [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (gridReady)="onGridReady($event)"
              (cellClickedEvent)="onCellClicked($event)">
            </ag-grid-angular>
          </div>
          <div class="my-20 flex-end">
            <div class="mr-10" *ngIf="revUserTotalCount">{{ 'GLOBAL.showing' | translate }} {{(currOffset * pageSize) + 1}}
              {{ 'GLOBAL.to-small' | translate }} {{rowData?.length > 1 ? currOffset*pageSize + rowData?.length - 1 :
              currOffset*pageSize + rowData?.length}}
              {{ 'GLOBAL.of-small' | translate }} {{revUserTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
            <pagination [offset]=currOffset [limit]="1" [range]="1" [size]='getPages(revUserTotalCount,pageSize)'
              (pageChange)="onPageChange($event)">
            </pagination>
          </div>
        </ng-container>
      </ng-template>
      <ng-container *ngIf="!rowData?.length && !isRevSourceReportLoading; else statusData">
        <div class="flex-center-col h-100-337">
          <img src="assets/images/layered-cards.svg" alt="No Data Found">
          <div class="header-3 fw-600 text-center">{{'PROFILE.no-data-found' | translate }}</div>
        </div>
      </ng-container>
      <ng-container *ngIf="isRevSourceReportLoading">
        <div class="flex-center h-100 mt-80">
          <application-loader></application-loader>
        </div>
      </ng-container>
    </div>
  </div>
</div>